@page "/findings"
@using HWSAuditPlatform.WebApp.Services
@using HWSAuditPlatform.WebApp.Models
@using HWSAuditPlatform.Domain.Enums
@using HWSAuditPlatform.WebApp.Components.Findings
@using HWSAuditPlatform.WebApp.Components.Shared
@inject IFindingApiService FindingApiService
@inject IFindingCategoryApiService FindingCategoryApiService
@inject IUserApiService UserApiService
@inject IOrganizationApiService OrganizationApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Findings Management - HWS Audit Platform</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">Findings Management</h2>
                    <p class="text-muted mb-0">Manage audit findings, assign responsibilities, and track corrective actions</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-primary" @onclick="ShowCreateFindingModal">
                        <i class="fas fa-plus"></i> Create Finding
                    </button>
                </div>
            </div>

            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading findings...</p>
                </div>
            }
            else
            {
                <!-- Statistics Cards -->
                @if (statistics != null)
                {
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-exclamation-triangle text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">Total Findings</h6>
                                            <h4 class="mb-0">@statistics.TotalFindings</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-clock text-warning"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">Open Findings</h6>
                                            <h4 class="mb-0">@statistics.OpenFindings</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-exclamation-circle text-danger"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">Overdue</h6>
                                            <h4 class="mb-0">@statistics.OverdueFindings</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                                <i class="fas fa-check-circle text-success"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="card-title mb-1">Resolved</h6>
                                            <h4 class="mb-0">@statistics.ClosedFindings</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <!-- Filters -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" placeholder="Search findings..." 
                                       @bind="searchTerm" @onkeypress="OnSearchKeyPress" />
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Severity</label>
                                <select class="form-select" @bind="selectedSeverity">
                                    <option value="">All Severities</option>
                                    <option value="@SeverityLevel.Critical">Critical</option>
                                    <option value="@SeverityLevel.Major">Major</option>
                                    <option value="@SeverityLevel.Minor">Minor</option>
                                    <option value="@SeverityLevel.Observation">Observation</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select class="form-select" @bind="selectedStatus">
                                    <option value="">All Statuses</option>
                                    <option value="@FindingStatus.Open">Open</option>
                                    <option value="@FindingStatus.UnderInvestigation">Under Investigation</option>
                                    <option value="@FindingStatus.PendingCorrectiveAction">Pending Corrective Action</option>
                                    <option value="@FindingStatus.PendingVerification">Pending Verification</option>
                                    <option value="@FindingStatus.Closed">Closed</option>
                                    <option value="@FindingStatus.Rejected">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Category</label>
                                <select class="form-select" @bind="selectedCategoryId">
                                    <option value="">All Categories</option>
                                    @if (categories != null)
                                    {
                                        @foreach (var category in categories)
                                        {
                                            <option value="@category.Id">@category.CategoryName</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Filter</label>
                                <select class="form-select" @bind="quickFilter">
                                    <option value="">All Findings</option>
                                    <option value="overdue">Overdue Only</option>
                                    <option value="unassigned">Unassigned</option>
                                    <option value="my-responsibility">My Responsibility</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" @onclick="ApplyFilters">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Findings List -->
                @if (findings?.Items?.Any() == true)
                {
                    <div class="row">
                        @foreach (var finding in findings.Items)
                        {
                            <div class="col-12 mb-3">
                                <FindingCard Finding="finding" 
                                           OnStatusChanged="OnFindingStatusChanged"
                                           OnCategoryAssigned="OnFindingCategoryAssigned"
                                           OnResponsibilityAssigned="OnFindingResponsibilityAssigned" />
                            </div>
                        }
                    </div>

                    <!-- Pagination -->
                    @if (findings.TotalPages > 1)
                    {
                        <nav aria-label="Findings pagination">
                            <ul class="pagination justify-content-center">
                                <li class="page-item @(findings.PageNumber <= 1 ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(findings.PageNumber - 1)" disabled="@(findings.PageNumber <= 1)">
                                        Previous
                                    </button>
                                </li>
                                
                                @for (int i = Math.Max(1, findings.PageNumber - 2); i <= Math.Min(findings.TotalPages, findings.PageNumber + 2); i++)
                                {
                                    var pageNumber = i;
                                    <li class="page-item @(findings.PageNumber == pageNumber ? "active" : "")">
                                        <button class="page-link" @onclick="() => GoToPage(pageNumber)">
                                            @pageNumber
                                        </button>
                                    </li>
                                }
                                
                                <li class="page-item @(findings.PageNumber >= findings.TotalPages ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => GoToPage(findings.PageNumber + 1)" disabled="@(findings.PageNumber >= findings.TotalPages)">
                                        Next
                                    </button>
                                </li>
                            </ul>
                        </nav>
                    }
                }
                else if (!isLoading)
                {
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No findings found</h5>
                        <p class="text-muted">Try adjusting your search criteria or create a new finding.</p>
                    </div>
                }
            }
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private PagedResult<FindingDto>? findings;
    private FindingStatisticsModel? statistics;
    private List<FindingCategoryDto>? categories;

    // Filter properties
    private string searchTerm = "";
    private string selectedSeverity = "";
    private string selectedStatus = "";
    private string selectedCategoryId = "";
    private string quickFilter = "";
    private int currentPage = 1;
    private int pageSize = 20;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load findings, statistics, and categories in parallel
            var findingsTask = LoadFindings();
            var statisticsTask = FindingApiService.GetFindingStatisticsAsync();
            var categoriesTask = FindingCategoryApiService.GetFindingCategoriesAsync();

            await Task.WhenAll(findingsTask, statisticsTask, categoriesTask);

            findings = await findingsTask;
            statistics = await statisticsTask;
            categories = await categoriesTask;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error loading findings data:", ex.Message);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task<PagedResult<FindingDto>?> LoadFindings()
    {
        SeverityLevel? severity = null;
        if (!string.IsNullOrEmpty(selectedSeverity) && Enum.TryParse<SeverityLevel>(selectedSeverity, out var parsedSeverity))
            severity = parsedSeverity;

        FindingStatus? status = null;
        if (!string.IsNullOrEmpty(selectedStatus) && Enum.TryParse<FindingStatus>(selectedStatus, out var parsedStatus))
            status = parsedStatus;

        int? categoryId = null;
        if (!string.IsNullOrEmpty(selectedCategoryId) && int.TryParse(selectedCategoryId, out var parsedCategoryId))
            categoryId = parsedCategoryId;

        bool? isOverdue = quickFilter == "overdue" ? true : null;
        string? responsibleUserId = quickFilter == "my-responsibility" ? "current-user" : null; // TODO: Get actual current user ID

        return await FindingApiService.GetFindingsAsync(
            searchTerm: string.IsNullOrWhiteSpace(searchTerm) ? null : searchTerm,
            severityLevel: severity,
            status: status,
            categoryId: categoryId,
            isOverdue: isOverdue,
            responsibleUserId: responsibleUserId,
            pageNumber: currentPage,
            pageSize: pageSize);
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private async Task ApplyFilters()
    {
        currentPage = 1;
        findings = await LoadFindings();
        StateHasChanged();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ApplyFilters();
        }
    }

    private async Task GoToPage(int page)
    {
        if (page >= 1 && page <= (findings?.TotalPages ?? 1))
        {
            currentPage = page;
            findings = await LoadFindings();
            StateHasChanged();
        }
    }

    private void ShowCreateFindingModal()
    {
        // TODO: Implement create finding modal
        JSRuntime.InvokeVoidAsync("alert", "Create finding functionality will be implemented soon.");
    }

    private async Task OnFindingStatusChanged(string findingId, FindingStatus newStatus)
    {
        try
        {
            var success = await FindingApiService.UpdateFindingStatusAsync(findingId, newStatus);
            if (success)
            {
                await RefreshData();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error updating finding status:", ex.Message);
        }
    }

    private async Task OnFindingCategoryAssigned(string findingId, int? categoryId)
    {
        try
        {
            var success = await FindingApiService.AssignFindingCategoryAsync(findingId, categoryId);
            if (success)
            {
                await RefreshData();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error assigning finding category:", ex.Message);
        }
    }

    private async Task OnFindingResponsibilityAssigned(string findingId, string? responsibleUserId)
    {
        try
        {
            var success = await FindingApiService.AssignFindingResponsibilityAsync(findingId, responsibleUserId);
            if (success)
            {
                await RefreshData();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error assigning finding responsibility:", ex.Message);
        }
    }
}
