using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.WebApp.Models;
using HWSAuditPlatform.Application.Scheduling.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.WebApp.Services;

/// <summary>
/// Debug implementation of audit API service that returns mock data
/// </summary>
public class DebugAuditApiService : IAuditApiService
{
    private readonly ILogger<DebugAuditApiService> _logger;
    private readonly List<Audit> _mockAudits;

    public DebugAuditApiService(ILogger<DebugAuditApiService> logger)
    {
        _logger = logger;
        _mockAudits = CreateMockAudits();
    }

    public async Task<IEnumerable<Audit>> GetAuditsAsync()
    {
        return await GetAuditsAsync(null, null, null);
    }

    public async Task<IEnumerable<Audit>> GetAuditsAsync(string? searchTerm = null, string? status = null, int? templateId = null)
    {
        await Task.Delay(200); // Simulate network delay
        _logger.LogWarning("🚨 DEBUG MODE: Returning filtered mock audits - Search: {SearchTerm}, Status: {Status}, TemplateId: {TemplateId}",
            searchTerm, status, templateId);

        var filteredAudits = _mockAudits.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var search = searchTerm.ToLower();
            filteredAudits = filteredAudits.Where(a =>
                (a.AuditTemplate?.TemplateName?.ToLower().Contains(search) == true) ||
                (a.Factory?.FactoryName?.ToLower().Contains(search) == true) ||
                (a.Area?.AreaName?.ToLower().Contains(search) == true));
        }

        // Apply status filter
        if (!string.IsNullOrWhiteSpace(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var statusEnum))
        {
            filteredAudits = filteredAudits.Where(a => a.OverallStatus == statusEnum);
        }

        // Apply template filter
        if (templateId.HasValue)
        {
            filteredAudits = filteredAudits.Where(a => a.AuditTemplate?.Id == templateId.Value);
        }

        return filteredAudits.ToList();
    }

    public async Task<Audit?> GetAuditByIdAsync(string id)
    {
        await Task.Delay(100);
        var audit = _mockAudits.FirstOrDefault(a => a.Id == id);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock audit with ID: {Id}", id);
        return audit;
    }

    public async Task<Audit> CreateAuditAsync(Audit audit)
    {
        await Task.Delay(300);
        audit.Id = $"audit-{_mockAudits.Count + 1:000}";
        _mockAudits.Add(audit);
        _logger.LogWarning("🚨 DEBUG MODE: Created mock audit with ID: {Id}", audit.Id);
        return audit;
    }

    public async Task<string> CreateAuditAsync(CreateAuditRequest request)
    {
        await Task.Delay(300); // Simulate network delay

        // Create a mock audit from the request
        var auditId = $"audit-{_mockAudits.Count + 1:000}";
        var audit = new Audit
        {
            Id = auditId,
            AuditTemplateId = request.AuditTemplateId,
            AssignmentType = request.AssignmentType,
            AssignedToUserGroupId = request.AssignedToUserGroupId,
            AssignedToUserId = request.AssignedToUserId,
            ScheduledDate = request.ScheduledDate,
            DueDate = request.DueDate,
            FactoryId = request.FactoryId,
            AreaId = request.AreaId,
            SubAreaId = request.SubAreaId,
            OverallStatus = AuditOverallStatus.Scheduled,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            // Mock related entities
            AuditTemplate = new AuditTemplate { Id = request.AuditTemplateId, TemplateName = "Mock Template" },
            Factory = new Factory { Id = request.FactoryId, FactoryName = "Mock Factory" },
            Area = new Area { Id = request.AreaId, AreaName = "Mock Area" }
        };

        _mockAudits.Add(audit);

        _logger.LogWarning("🚨 DEBUG MODE: Created mock audit with ID: {Id}", auditId);
        return auditId;
    }

    public async Task<Audit> UpdateAuditAsync(Audit audit)
    {
        await Task.Delay(200);
        var existingIndex = _mockAudits.FindIndex(a => a.Id == audit.Id);
        if (existingIndex >= 0)
        {
            _mockAudits[existingIndex] = audit;
        }
        _logger.LogWarning("🚨 DEBUG MODE: Updated mock audit with ID: {Id}", audit.Id);
        return audit;
    }

    public async Task<bool> DeleteAuditAsync(string id)
    {
        await Task.Delay(150);
        var removed = _mockAudits.RemoveAll(a => a.Id == id) > 0;
        _logger.LogWarning("🚨 DEBUG MODE: Deleted mock audit with ID: {Id}, Success: {Success}", id, removed);
        return removed;
    }

    public async Task<Application.Audits.DTOs.AuditReviewDto?> GetAuditForReviewAsync(string id)
    {
        await Task.Delay(150);
        var audit = _mockAudits.FirstOrDefault(a => a.Id == id);

        if (audit == null)
        {
            _logger.LogWarning("🚨 DEBUG MODE: Mock audit with ID {Id} not found for review", id);
            return null;
        }

        // Create a mock detailed audit DTO for review
        var auditDto = new Application.Audits.DTOs.AuditReviewDto
        {
            Id = audit.Id,
            AuditTemplateName = audit.AuditTemplate?.TemplateName ?? "Mock Safety Inspection",
            AssignedToUserId = audit.AssignedToUserId,
            AssignedToUserName = audit.AssignedToUser?.FullName ?? "Debug User",
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            OverallStatus = audit.OverallStatus,
            FactoryName = audit.Factory?.FactoryName ?? "Debug Manufacturing Plant",
            AreaName = audit.Area?.AreaName ?? "Production Line A",
            SubAreaName = null,
            OverallScore = audit.OverallScore,
            ManagerComments = audit.ManagerComments,
            ReviewedByUserName = "Debug Manager",
            ReviewedAt = audit.ReviewedAt,
            IsOverdue = audit.IsOverdue,

            // Add mock answers for review
            Answers = CreateMockAnswers(),
            TotalQuestions = 3,
            AnsweredQuestions = 3,
            PassedQuestions = 2,
            FailedQuestions = 1,
            NotApplicableQuestions = 0,
            QuestionsWithFindings = 1,
            TotalAttachments = 1
        };

        _logger.LogWarning("🚨 DEBUG MODE: Returning mock audit DTO for review with ID: {Id}", id);
        return auditDto;
    }

    public async Task<bool> ReviewAuditAsync(string id, ReviewAuditRequest request)
    {
        await Task.Delay(200);
        var audit = _mockAudits.FirstOrDefault(a => a.Id == id);

        if (audit == null)
        {
            _logger.LogWarning("🚨 DEBUG MODE: Mock audit with ID {Id} not found for review", id);
            return false;
        }

        // Update the audit with review information
        audit.ManagerComments = request.ManagerComments;
        audit.ReviewedAt = DateTime.UtcNow;
        audit.ReviewedByUserId = "debug-manager-001";
        audit.OverallStatus = request.Approved ? AuditOverallStatus.Closed : AuditOverallStatus.InProgress;

        _logger.LogWarning("🚨 DEBUG MODE: Reviewed mock audit with ID: {Id}, Approved: {Approved}", id, request.Approved);
        return true;
    }

    public async Task<IEnumerable<FindingDto>> GetAuditFindingsAsync(string auditId)
    {
        await Task.Delay(150);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock findings for audit {AuditId}", auditId);

        // Return mock findings for the audit
        var mockFindings = new List<FindingDto>
        {
            new()
            {
                Id = $"finding-{auditId}-001",
                FindingCode = "FND-2025-001",
                FindingDescription = "Safety equipment not properly maintained",
                FindingSeverityLevel = SeverityLevel.Major,
                Status = "Open",
                IsOpen = true,
                IsOverdue = false,
                AuditId = auditId,
                AuditTitle = "Safety Inspection",
                AreaName = "Production Area A",
                FactoryName = "Main Factory"
            },
            new()
            {
                Id = $"finding-{auditId}-002",
                FindingCode = "FND-2025-002",
                FindingDescription = "Documentation incomplete",
                FindingSeverityLevel = SeverityLevel.Minor,
                Status = "Closed",
                IsOpen = false,
                IsOverdue = false,
                AuditId = auditId,
                AuditTitle = "Safety Inspection",
                AreaName = "Production Area A",
                FactoryName = "Main Factory"
            }
        };

        return mockFindings;
    }

    public async Task<HWSAuditPlatform.WebApp.Models.PagedResult<FindingDto>> GetFindingsAsync(FindingFilterModel filter)
    {
        await Task.Delay(200);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock findings with filter");

        var mockFindings = new List<FindingDto>
        {
            new()
            {
                Id = "finding-001",
                FindingCode = "FND-2025-001",
                FindingDescription = "Safety equipment not properly maintained",
                FindingSeverityLevel = SeverityLevel.Major,
                Status = "Open",
                IsOpen = true,
                IsOverdue = false,
                AuditId = "audit-001",
                AuditTitle = "Safety Inspection",
                AreaName = "Production Area A",
                FactoryName = "Main Factory"
            },
            new()
            {
                Id = "finding-002",
                FindingCode = "FND-2025-002",
                FindingDescription = "Documentation incomplete",
                FindingSeverityLevel = SeverityLevel.Minor,
                Status = "Closed",
                IsOpen = false,
                IsOverdue = false,
                AuditId = "audit-002",
                AuditTitle = "Quality Control Audit",
                AreaName = "Quality Lab",
                FactoryName = "Main Factory"
            }
        };

        return new HWSAuditPlatform.WebApp.Models.PagedResult<FindingDto>(mockFindings, filter.PageNumber, filter.PageSize, mockFindings.Count);
    }

    public async Task<FindingStatisticsModel> GetFindingStatisticsAsync()
    {
        await Task.Delay(100);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock finding statistics");

        return new FindingStatisticsModel
        {
            TotalFindings = 25,
            OpenFindings = 15,
            ClosedFindings = 10,
            OverdueFindings = 3,
            CategorizedFindings = 20,
            UncategorizedFindings = 5,
            CriticalFindings = 2,
            MajorFindings = 8,
            MinorFindings = 12,
            ObservationFindings = 3,
            FindingsWithResponsibleUser = 18,
            FindingsWithAnalyst = 12,
            AverageResolutionDays = 7.5,
            FindingsCreatedThisMonth = 8,
            FindingsClosedThisMonth = 6
        };
    }

    public async Task<bool> AssignFindingCategoryAsync(string findingId, int? categoryId)
    {
        await Task.Delay(100);
        _logger.LogWarning("🚨 DEBUG MODE: Assigning category {CategoryId} to finding {FindingId}", categoryId, findingId);
        return true;
    }

    public async Task<bool> AssignFindingResponsibleUserAsync(string findingId, string? userId)
    {
        await Task.Delay(100);
        _logger.LogWarning("🚨 DEBUG MODE: Assigning responsible user {UserId} to finding {FindingId}", userId, findingId);
        return true;
    }

    public async Task<bool> BulkUpdateFindingsAsync(BulkFindingOperationModel operation)
    {
        await Task.Delay(200);
        _logger.LogWarning("🚨 DEBUG MODE: Performing bulk operation {Operation} on {Count} findings", operation.Operation, operation.FindingIds.Count);
        return true;
    }

    private List<Application.Templates.DTOs.QuestionDto> CreateMockQuestions()
    {
        return new List<Application.Templates.DTOs.QuestionDto>
        {
            new()
            {
                Id = 1,
                AuditTemplateId = 1,
                QuestionText = "Are all safety equipment properly maintained?",
                HelpText = "Check that all safety equipment is in working condition",
                QuestionType = QuestionType.YesNo,
                IsRequired = true,
                Weight = 10,
                DisplayOrder = 1,
                IsActive = true
            },
            new()
            {
                Id = 2,
                AuditTemplateId = 1,
                QuestionText = "What is the current temperature reading?",
                HelpText = "Record the temperature from the main gauge",
                QuestionType = QuestionType.Numeric,
                IsRequired = true,
                Weight = 5,
                DisplayOrder = 2,
                IsActive = true
            },
            new()
            {
                Id = 3,
                AuditTemplateId = 1,
                QuestionText = "Additional observations",
                HelpText = "Any additional notes or observations",
                QuestionType = QuestionType.LongText,
                IsRequired = false,
                Weight = 2,
                DisplayOrder = 3,
                IsActive = true
            }
        };
    }

    private List<Application.Audits.DTOs.AuditAnswerDto> CreateMockAnswers()
    {
        return new List<Application.Audits.DTOs.AuditAnswerDto>
        {
            new()
            {
                Id = "answer-001",
                AuditId = "audit-001",
                QuestionId = 1,
                QuestionText = "Are all safety equipment properly maintained?",
                QuestionType = QuestionType.YesNo,
                AnswerValue = "False",
                IsNotApplicable = false,
                Comments = "Some equipment needs maintenance",
                SeverityLevel = SeverityLevel.Major,
                CreatedAt = DateTime.UtcNow.AddHours(-2),
                UpdatedAt = DateTime.UtcNow.AddHours(-2),
                FailureReasons = new List<Application.Audits.DTOs.AuditAnswerFailureReasonDto>
                {
                    new()
                    {
                        Id = "reason-001",
                        AuditAnswerId = "answer-001",
                        FailureReasonText = "Fire extinguisher needs refilling"
                    },
                    new()
                    {
                        Id = "reason-002",
                        AuditAnswerId = "answer-001",
                        FailureReasonText = "Safety harness has worn straps"
                    }
                },
                Attachments = new List<Application.Audits.DTOs.AuditAttachmentDto>
                {
                    new()
                    {
                        Id = "attachment-001",
                        AuditAnswerId = "answer-001",
                        FileName = "safety_equipment_photo.jpg",
                        FileSize = 1024000,
                        ContentType = "image/jpeg"
                    }
                }
            },
            new()
            {
                Id = "answer-002",
                AuditId = "audit-001",
                QuestionId = 2,
                QuestionText = "What is the current temperature reading?",
                QuestionType = QuestionType.Numeric,
                AnswerValue = "23.5",
                IsNotApplicable = false,
                Comments = "Temperature is within normal range",
                CreatedAt = DateTime.UtcNow.AddHours(-2),
                UpdatedAt = DateTime.UtcNow.AddHours(-2)
            },
            new()
            {
                Id = "answer-003",
                AuditId = "audit-001",
                QuestionId = 3,
                QuestionText = "Additional observations",
                QuestionType = QuestionType.LongText,
                AnswerValue = "Overall facility is in good condition, minor maintenance needed",
                IsNotApplicable = false,
                CreatedAt = DateTime.UtcNow.AddHours(-2),
                UpdatedAt = DateTime.UtcNow.AddHours(-2)
            }
        };
    }

    private List<Audit> CreateMockAudits()
    {
        var mockFactory = new Factory { Id = 1, FactoryName = "Debug Manufacturing Plant" };
        var mockArea = new Area { Id = 1, AreaName = "Production Line A" };
        var mockTemplate = new AuditTemplate { Id = 1, TemplateName = "Safety Inspection Protocol" };
        var mockUser = new User { Id = "debug-user-001", FirstName = "Debug", LastName = "User", Username = "debug.user" };

        return new List<Audit>
        {
            new()
            {
                Id = "audit-001",
                AuditTemplateId = 1,
                AuditTemplate = mockTemplate,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "debug-user-001",
                AssignedToUser = mockUser,
                ScheduledDate = DateTime.UtcNow.AddDays(-2),
                DueDate = DateTime.UtcNow.AddDays(3),
                OverallStatus = AuditOverallStatus.InProgress,
                FactoryId = 1,
                Factory = mockFactory,
                AreaId = 1,
                Area = mockArea,
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                CreatedByUserId = "debug-user-001"
            },
            new()
            {
                Id = "audit-002",
                AuditTemplateId = 1,
                AuditTemplate = mockTemplate,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "debug-user-001",
                AssignedToUser = mockUser,
                ScheduledDate = DateTime.UtcNow.AddDays(-7),
                DueDate = DateTime.UtcNow.AddDays(-2),
                OverallStatus = AuditOverallStatus.PendingManagerReview,
                FactoryId = 1,
                Factory = mockFactory,
                AreaId = 1,
                Area = mockArea,
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                CreatedByUserId = "debug-user-001"
            },
            new()
            {
                Id = "audit-003",
                AuditTemplateId = 1,
                AuditTemplate = mockTemplate,
                AssignmentType = AssignmentType.Individual,
                AssignedToUserId = "debug-user-001",
                AssignedToUser = mockUser,
                ScheduledDate = DateTime.UtcNow.AddDays(-15),
                DueDate = DateTime.UtcNow.AddDays(-10),
                StartedAt = DateTime.UtcNow.AddDays(-13),
                CompletedAt = DateTime.UtcNow.AddDays(-11),
                OverallStatus = AuditOverallStatus.Closed,
                FactoryId = 1,
                Factory = mockFactory,
                AreaId = 1,
                Area = mockArea,
                CreatedAt = DateTime.UtcNow.AddDays(-18),
                CreatedByUserId = "debug-user-001"
            }
        };
    }
}

/// <summary>
/// Debug implementation of template API service that returns mock data
/// </summary>
public class DebugTemplateApiService : ITemplateApiService
{
    private readonly ILogger<DebugTemplateApiService> _logger;
    private readonly List<AuditTemplate> _mockTemplates;

    public DebugTemplateApiService(ILogger<DebugTemplateApiService> logger)
    {
        _logger = logger;
        _mockTemplates = CreateMockTemplates();
    }

    public async Task<IEnumerable<AuditTemplate>> GetTemplatesAsync(string? searchTerm = null, bool? isActive = null, int pageSize = 100)
    {
        await Task.Delay(150);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock templates with search term: {SearchTerm}, isActive: {IsActive}", searchTerm, isActive);

        var filteredTemplates = _mockTemplates.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredTemplates = filteredTemplates.Where(t =>
                t.TemplateName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (t.Description?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
        }

        // Apply active filter
        if (isActive.HasValue)
        {
            filteredTemplates = filteredTemplates.Where(t => t.IsActive == isActive.Value);
        }

        return filteredTemplates.Take(pageSize);
    }

    public async Task<AuditTemplate?> GetTemplateByIdAsync(int id)
    {
        await Task.Delay(100);
        var template = _mockTemplates.FirstOrDefault(t => t.Id == id);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock template with ID: {Id}", id);
        return template;
    }

    public async Task<AuditTemplate> CreateTemplateAsync(AuditTemplate template)
    {
        await Task.Delay(300);
        template.Id = _mockTemplates.Count + 1;
        _mockTemplates.Add(template);
        _logger.LogWarning("🚨 DEBUG MODE: Created mock template with ID: {Id}", template.Id);
        return template;
    }

    public async Task<AuditTemplate> UpdateTemplateAsync(AuditTemplate template)
    {
        await Task.Delay(200);
        var existingIndex = _mockTemplates.FindIndex(t => t.Id == template.Id);
        if (existingIndex >= 0)
        {
            _mockTemplates[existingIndex] = template;
        }
        _logger.LogWarning("🚨 DEBUG MODE: Updated mock template with ID: {Id}", template.Id);
        return template;
    }

    public async Task<bool> DeleteTemplateAsync(int id)
    {
        await Task.Delay(150);
        var removed = _mockTemplates.RemoveAll(t => t.Id == id) > 0;
        _logger.LogWarning("🚨 DEBUG MODE: Deleted mock template with ID: {Id}, Success: {Success}", id, removed);
        return removed;
    }

    public async Task<int?> CreateTemplateAsync(CreateAuditTemplateRequest request)
    {
        await Task.Delay(300);
        var mockId = _mockTemplates.Count + 1;
        _logger.LogWarning("🚨 DEBUG MODE: Created mock template with ID: {Id}", mockId);
        return mockId;
    }

    public async Task<int?> AddQuestionToTemplateAsync(int templateId, CreateQuestionRequest request)
    {
        await Task.Delay(200);
        var mockQuestionId = new Random().Next(100, 999);
        _logger.LogWarning("🚨 DEBUG MODE: Added mock question with ID: {QuestionId} to template {TemplateId}", mockQuestionId, templateId);
        return mockQuestionId;
    }

    public async Task<bool> UpdateQuestionAsync(int questionId, UpdateQuestionRequest request)
    {
        await Task.Delay(150);
        _logger.LogWarning("🚨 DEBUG MODE: Updated mock question with ID: {QuestionId}", questionId);
        return true;
    }

    public async Task<bool> DeleteQuestionAsync(int questionId)
    {
        await Task.Delay(100);
        _logger.LogWarning("🚨 DEBUG MODE: Deleted mock question with ID: {QuestionId}", questionId);
        return true;
    }

    public async Task<bool> UpdateTemplateAsync(int id, UpdateAuditTemplateRequest request)
    {
        await Task.Delay(200);
        var template = _mockTemplates.FirstOrDefault(t => t.Id == id);
        if (template != null)
        {
            template.TemplateName = request.TemplateName;
            template.Description = request.Description;
            template.IsActive = request.IsActive;
            template.UpdatedAt = DateTime.UtcNow;
        }
        _logger.LogWarning("🚨 DEBUG MODE: Updated mock template with ID: {Id}", id);
        return template != null;
    }

    public async Task<int?> CreateTemplateVersionAsync(int originalTemplateId, string? newTemplateName = null, string? newDescription = null)
    {
        await Task.Delay(300);
        var originalTemplate = _mockTemplates.FirstOrDefault(t => t.Id == originalTemplateId);
        if (originalTemplate != null)
        {
            var newTemplateId = _mockTemplates.Count + 1;
            var newTemplate = new AuditTemplate
            {
                Id = newTemplateId,
                TemplateName = newTemplateName ?? originalTemplate.TemplateName,
                Description = newDescription ?? originalTemplate.Description,
                Version = originalTemplate.Version + 1,
                IsPublished = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedByUserId = "debug-user-001"
            };
            _mockTemplates.Add(newTemplate);
            _logger.LogWarning("🚨 DEBUG MODE: Created mock template version with ID: {Id} from original {OriginalId}", newTemplateId, originalTemplateId);
            return newTemplateId;
        }
        return null;
    }

    public async Task<bool> PublishTemplateAsync(int id)
    {
        await Task.Delay(150);
        var template = _mockTemplates.FirstOrDefault(t => t.Id == id);
        if (template != null)
        {
            template.IsPublished = true;
            template.UpdatedAt = DateTime.UtcNow;
        }
        _logger.LogWarning("🚨 DEBUG MODE: Published mock template with ID: {Id}", id);
        return template != null;
    }

    public async Task<PublishTemplateResult> PublishTemplateWithDetailsAsync(int id)
    {
        await Task.Delay(150);
        var template = _mockTemplates.FirstOrDefault(t => t.Id == id);

        if (template == null)
        {
            _logger.LogWarning("🚨 DEBUG MODE: Template {Id} not found for publishing", id);
            return PublishTemplateResult.Failed("Template not found");
        }

        // Simulate validation - check if template has questions
        var hasQuestions = template.Questions?.Any(q => q.IsActive) == true;
        if (!hasQuestions)
        {
            _logger.LogWarning("🚨 DEBUG MODE: Template {Id} has no questions - validation failed", id);
            return PublishTemplateResult.ValidationFailed(new List<string>
            {
                "Audit template must have at least one question to be published"
            });
        }

        template.IsPublished = true;
        template.UpdatedAt = DateTime.UtcNow;
        _logger.LogWarning("🚨 DEBUG MODE: Published mock template with ID: {Id}", id);
        return PublishTemplateResult.Success();
    }

    public async Task<bool> UpdateTemplateFeatureFlagsAsync(int templateId, bool enableAreaBasedResponsibility, bool enableFindingCategorization)
    {
        await Task.Delay(150);
        var template = _mockTemplates.FirstOrDefault(t => t.Id == templateId);
        if (template != null)
        {
            // Mock updating feature flags - in real implementation this would update the database
            _logger.LogWarning("🚨 DEBUG MODE: Updated feature flags for template {TemplateId} - AreaResponsibility: {AreaResponsibility}, Categorization: {Categorization}",
                templateId, enableAreaBasedResponsibility, enableFindingCategorization);
        }
        return template != null;
    }

    public async Task<IEnumerable<AuditTemplate>> GetTemplatesWithAreaResponsibilityAsync()
    {
        await Task.Delay(100);
        var templatesWithAreaResponsibility = _mockTemplates.Where(t => t.IsActive).Take(2); // Mock: return first 2 active templates
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock templates with area responsibility", templatesWithAreaResponsibility.Count());
        return templatesWithAreaResponsibility;
    }

    public async Task<IEnumerable<AuditTemplate>> GetTemplatesWithCategorizationAsync()
    {
        await Task.Delay(100);
        var templatesWithCategorization = _mockTemplates.Where(t => t.IsActive).Skip(1).Take(2); // Mock: return different set of templates
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock templates with categorization", templatesWithCategorization.Count());
        return templatesWithCategorization;
    }

    private List<AuditTemplate> CreateMockTemplates()
    {
        return new List<AuditTemplate>
        {
            new()
            {
                Id = 1,
                TemplateName = "Safety Inspection Protocol",
                Description = "Comprehensive safety inspection checklist for manufacturing areas",
                Version = 1,
                IsPublished = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-30),
                CreatedByUserId = "debug-user-001",
                Questions = new List<Question>(),
                QuestionGroups = new List<QuestionGroup>()
            },
            new()
            {
                Id = 2,
                TemplateName = "Quality Control Audit",
                Description = "Quality control procedures and standards verification",
                Version = 2,
                IsPublished = false,
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-25),
                UpdatedAt = DateTime.UtcNow.AddDays(-25),
                CreatedByUserId = "debug-user-001",
                Questions = new List<Question>(),
                QuestionGroups = new List<QuestionGroup>()
            },
            new()
            {
                Id = 3,
                TemplateName = "Environmental Compliance Check",
                Description = "Environmental regulations and compliance verification",
                Version = 1,
                IsPublished = true,
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                UpdatedAt = DateTime.UtcNow.AddDays(-20),
                CreatedByUserId = "debug-user-001",
                Questions = new List<Question>(),
                QuestionGroups = new List<QuestionGroup>()
            }
        };
    }
}

/// <summary>
/// Debug implementation of user API service that returns mock data
/// </summary>
public class DebugUserApiService : IUserApiService
{
    private readonly ILogger<DebugUserApiService> _logger;
    private readonly List<User> _mockUsers;

    public DebugUserApiService(ILogger<DebugUserApiService> logger)
    {
        _logger = logger;
        _mockUsers = CreateMockUsers();
    }

    public async Task<IEnumerable<User>> GetUsersAsync()
    {
        await Task.Delay(120);
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock users", _mockUsers.Count);
        return _mockUsers;
    }

    public async Task<User?> GetUserByIdAsync(string id)
    {
        await Task.Delay(80);
        var user = _mockUsers.FirstOrDefault(u => u.Id == id);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock user with ID: {Id}", id);
        return user;
    }

    public async Task<User> CreateUserAsync(User user)
    {
        await Task.Delay(250);
        user.Id = $"user-{_mockUsers.Count + 1:000}";
        _mockUsers.Add(user);
        _logger.LogWarning("🚨 DEBUG MODE: Created mock user with ID: {Id}", user.Id);
        return user;
    }

    public async Task<User> UpdateUserAsync(User user)
    {
        await Task.Delay(180);
        var existingIndex = _mockUsers.FindIndex(u => u.Id == user.Id);
        if (existingIndex >= 0)
        {
            _mockUsers[existingIndex] = user;
        }
        _logger.LogWarning("🚨 DEBUG MODE: Updated mock user with ID: {Id}", user.Id);
        return user;
    }

    public async Task<bool> DeleteUserAsync(string id)
    {
        await Task.Delay(120);
        var removed = _mockUsers.RemoveAll(u => u.Id == id) > 0;
        _logger.LogWarning("🚨 DEBUG MODE: Deleted mock user with ID: {Id}, Success: {Success}", id, removed);
        return removed;
    }

    private List<User> CreateMockUsers()
    {
        return new List<User>
        {
            new()
            {
                Id = "debug-user-001",
                Username = "debug.user",
                FirstName = "Debug",
                LastName = "User",
                Email = "<EMAIL>",
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-60),
                CreatedByUserId = "system"
            },
            new()
            {
                Id = "manager-001",
                Username = "manager.user",
                FirstName = "Manager",
                LastName = "User",
                Email = "<EMAIL>",
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-50),
                CreatedByUserId = "system"
            },
            new()
            {
                Id = "auditor-001",
                Username = "auditor.user",
                FirstName = "Auditor",
                LastName = "User",
                Email = "<EMAIL>",
                IsActive = true,
                CreatedAt = DateTime.UtcNow.AddDays(-40),
                CreatedByUserId = "system"
            }
        };
    }
}

/// <summary>
/// Debug implementation of organizational API service that returns mock data
/// </summary>
public class DebugOrganizationApiService : IOrganizationApiService
{
    private readonly ILogger<DebugOrganizationApiService> _logger;
    private readonly List<FactorySummary> _mockFactories;
    private readonly List<AreaSummary> _mockAreas;
    private readonly List<SubAreaSummary> _mockSubAreas;
    private readonly List<UserSummary> _mockUsers;
    private readonly List<UserGroupSummary> _mockUserGroups;

    public DebugOrganizationApiService(ILogger<DebugOrganizationApiService> logger)
    {
        _logger = logger;
        _mockFactories = CreateMockFactories();
        _mockAreas = CreateMockAreas();
        _mockSubAreas = CreateMockSubAreas();
        _mockUsers = CreateMockUserSummaries();
        _mockUserGroups = CreateMockUserGroups();
    }

    public async Task<IEnumerable<FactorySummary>> GetFactoriesAsync()
    {
        await Task.Delay(100);
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock factories", _mockFactories.Count);
        return _mockFactories;
    }

    public async Task<IEnumerable<AreaSummary>> GetAreasByFactoryAsync(int factoryId)
    {
        await Task.Delay(80);
        var areas = _mockAreas.Where(a => a.FactoryId == factoryId).ToList();
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock areas for factory {FactoryId}", areas.Count, factoryId);
        return areas;
    }

    public async Task<IEnumerable<SubAreaSummary>> GetSubAreasByAreaAsync(int areaId)
    {
        await Task.Delay(60);
        var subAreas = _mockSubAreas.Where(sa => sa.AreaId == areaId).ToList();
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock sub-areas for area {AreaId}", subAreas.Count, areaId);
        return subAreas;
    }

    public async Task<IEnumerable<UserSummary>> GetUsersAsync()
    {
        await Task.Delay(120);
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock users", _mockUsers.Count);
        return _mockUsers;
    }

    public async Task<IEnumerable<UserSummary>> SearchUsersAsync(string searchTerm, int pageSize = 10)
    {
        await Task.Delay(200); // Simulate network delay

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return Enumerable.Empty<UserSummary>();
        }

        var searchTermLower = searchTerm.ToLowerInvariant();
        var filteredUsers = _mockUsers
            .Where(u => u.Username.ToLowerInvariant().Contains(searchTermLower) ||
                       u.FullName.ToLowerInvariant().Contains(searchTermLower))
            .Take(pageSize)
            .ToList();

        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock users for search term: {SearchTerm}", filteredUsers.Count, searchTerm);
        return filteredUsers;
    }

    public async Task<IEnumerable<UserGroupSummary>> GetUserGroupsAsync()
    {
        await Task.Delay(90);
        _logger.LogWarning("🚨 DEBUG MODE: Returning {Count} mock user groups", _mockUserGroups.Count);
        return _mockUserGroups;
    }

    private List<FactorySummary> CreateMockFactories()
    {
        return new List<FactorySummary>
        {
            new() { Id = 1, FactoryName = "Debug Manufacturing Plant", LocationName = "Debug City", IsActive = true },
            new() { Id = 2, FactoryName = "Test Production Facility", LocationName = "Test Town", IsActive = true },
            new() { Id = 3, FactoryName = "Mock Assembly Center", LocationName = "Mock Valley", IsActive = false }
        };
    }

    private List<AreaSummary> CreateMockAreas()
    {
        return new List<AreaSummary>
        {
            new() { Id = 1, AreaName = "Production Line A", FactoryId = 1, FactoryName = "Debug Manufacturing Plant", IsActive = true },
            new() { Id = 2, AreaName = "Quality Control", FactoryId = 1, FactoryName = "Debug Manufacturing Plant", IsActive = true },
            new() { Id = 3, AreaName = "Packaging Department", FactoryId = 1, FactoryName = "Debug Manufacturing Plant", IsActive = true },
            new() { Id = 4, AreaName = "Assembly Line B", FactoryId = 2, FactoryName = "Test Production Facility", IsActive = true },
            new() { Id = 5, AreaName = "Warehouse", FactoryId = 2, FactoryName = "Test Production Facility", IsActive = true }
        };
    }

    private List<SubAreaSummary> CreateMockSubAreas()
    {
        return new List<SubAreaSummary>
        {
            new() { Id = 1, SubAreaName = "Welding Station 1", AreaId = 1, AreaName = "Production Line A", IsActive = true },
            new() { Id = 2, SubAreaName = "Welding Station 2", AreaId = 1, AreaName = "Production Line A", IsActive = true },
            new() { Id = 3, SubAreaName = "Paint Booth", AreaId = 1, AreaName = "Production Line A", IsActive = true },
            new() { Id = 4, SubAreaName = "Inspection Station", AreaId = 2, AreaName = "Quality Control", IsActive = true },
            new() { Id = 5, SubAreaName = "Testing Lab", AreaId = 2, AreaName = "Quality Control", IsActive = true }
        };
    }

    private List<UserSummary> CreateMockUserSummaries()
    {
        return new List<UserSummary>
        {
            new() { Id = "debug-user-001", Username = "debug.user", FullName = "Debug User", Email = "<EMAIL>", Role = "Auditor", IsActive = true, FactoryName = "Debug Manufacturing Plant", AdObjectGuid = "12345678-1234-1234-1234-123456789001" },
            new() { Id = "manager-001", Username = "manager.user", FullName = "Manager User", Email = "<EMAIL>", Role = "ProcessOwner", IsActive = true, FactoryName = "Debug Manufacturing Plant", AdObjectGuid = "12345678-1234-1234-1234-123456789002" },
            new() { Id = "auditor-001", Username = "auditor.user", FullName = "Auditor User", Email = "<EMAIL>", Role = "Auditor", IsActive = true, FactoryName = "Test Production Facility", AdObjectGuid = "12345678-1234-1234-1234-123456789003" },
            new() { Id = "admin-001", Username = "admin.user", FullName = "Admin User", Email = "<EMAIL>", Role = "DevAdmin", IsActive = true, FactoryName = "Debug Manufacturing Plant", AdObjectGuid = "12345678-1234-1234-1234-123456789004" }
        };
    }

    private List<UserGroupSummary> CreateMockUserGroups()
    {
        return new List<UserGroupSummary>
        {
            new() { Id = "group-001", GroupName = "Production Team", Description = "Production line workers", IsActive = true, MemberCount = 15 },
            new() { Id = "group-002", GroupName = "Quality Inspectors", Description = "Quality control team", IsActive = true, MemberCount = 8 },
            new() { Id = "group-003", GroupName = "Safety Officers", Description = "Safety and compliance team", IsActive = true, MemberCount = 5 },
            new() { Id = "group-004", GroupName = "Maintenance Crew", Description = "Equipment maintenance team", IsActive = true, MemberCount = 12 }
        };
    }
}

/// <summary>
/// Debug implementation of recurring audit API service that returns mock data
/// </summary>
public class DebugRecurringAuditApiService : IRecurringAuditApiService
{
    private readonly ILogger<DebugRecurringAuditApiService> _logger;
    private readonly List<RecurringAuditSettingSummaryDto> _mockSettings;

    public DebugRecurringAuditApiService(ILogger<DebugRecurringAuditApiService> logger)
    {
        _logger = logger;
        _mockSettings = CreateMockRecurringAuditSettings();
    }

    public async Task<PaginatedResult<RecurringAuditSettingSummaryDto>> GetRecurringAuditSettingsAsync(
        int pageNumber = 1, int pageSize = 20, string? searchTerm = null, bool? isEnabled = null,
        int? auditTemplateId = null, int? factoryId = null, int? areaId = null, int? subAreaId = null)
    {
        await Task.Delay(200);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock recurring audit settings");

        var filteredSettings = _mockSettings.AsQueryable();

        if (!string.IsNullOrEmpty(searchTerm))
            filteredSettings = filteredSettings.Where(s => s.SettingName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));

        if (isEnabled.HasValue)
            filteredSettings = filteredSettings.Where(s => s.IsEnabled == isEnabled.Value);

        var totalCount = filteredSettings.Count();
        var items = filteredSettings.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();

        return new PaginatedResult<RecurringAuditSettingSummaryDto>(
            items, totalCount, pageNumber, pageSize);
    }

    public async Task<RecurringAuditSettingDto?> GetRecurringAuditSettingByIdAsync(string id)
    {
        await Task.Delay(100);
        _logger.LogWarning("🚨 DEBUG MODE: Returning mock recurring audit setting for ID: {Id}", id);

        var summary = _mockSettings.FirstOrDefault(s => s.Id == id);
        if (summary == null) return null;

        return new RecurringAuditSettingDto
        {
            Id = summary.Id,
            SettingName = summary.SettingName,
            AuditTemplateName = summary.AuditTemplateName,
            IsEnabled = summary.IsEnabled,
            AssignmentType = summary.AssignmentType,
            AssignToUserName = summary.AssignToUserName,
            AssignToUserGroupName = summary.AssignToUserGroupName,
            FactoryName = summary.FactoryName,
            AreaName = summary.AreaName,
            SubAreaName = summary.SubAreaName,
            NextGenerationDate = summary.NextGenerationDate,
            LastGeneratedAt = summary.LastGeneratedAt,
            IsReadyToGenerate = summary.IsReadyToGenerate,
            DeadlineDays = 7,
            AuditTemplateId = 1,
            FactoryId = 1,
            CreatedAt = DateTime.UtcNow.AddDays(-30),
            UpdatedAt = DateTime.UtcNow.AddDays(-1)
        };
    }

    public async Task<string> CreateRecurringAuditSettingAsync(CreateRecurringAuditSettingRequest request)
    {
        await Task.Delay(300);
        var newId = $"setting-{_mockSettings.Count + 1:000}";
        _logger.LogWarning("🚨 DEBUG MODE: Created mock recurring audit setting with ID: {Id}", newId);
        return newId;
    }

    public async Task<bool> UpdateRecurringAuditSettingAsync(string id, UpdateRecurringAuditSettingRequest request)
    {
        await Task.Delay(200);
        _logger.LogWarning("🚨 DEBUG MODE: Updated mock recurring audit setting: {Id}", id);
        return true;
    }

    public async Task<bool> DeleteRecurringAuditSettingAsync(string id)
    {
        await Task.Delay(150);
        _logger.LogWarning("🚨 DEBUG MODE: Deleted mock recurring audit setting: {Id}", id);
        return true;
    }

    public async Task<bool> ToggleRecurringAuditSettingAsync(string id)
    {
        await Task.Delay(100);
        _logger.LogWarning("🚨 DEBUG MODE: Toggled mock recurring audit setting: {Id}", id);
        return true;
    }

    public async Task<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult> GenerateRecurringAuditsAsync(
        string? settingId = null, bool dryRun = false, int maxAuditsToGenerate = 100)
    {
        await Task.Delay(500);
        _logger.LogWarning("🚨 DEBUG MODE: Generated mock recurring audits");
        return new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult
        {
            SettingsProcessed = 1,
            AuditsGenerated = 3,
            ErrorsEncountered = 0,
            GeneratedAuditIds = new List<string> { "audit-001", "audit-002", "audit-003" }
        };
    }

    public async Task<Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult> PreviewRecurringAuditGenerationAsync(string settingId)
    {
        await Task.Delay(200);
        _logger.LogWarning("🚨 DEBUG MODE: Previewed mock recurring audit generation for: {SettingId}", settingId);
        return new Application.Scheduling.Commands.GenerateRecurringAudits.GenerateRecurringAuditsResult
        {
            SettingsProcessed = 1,
            AuditsGenerated = 2,
            ErrorsEncountered = 0,
            GeneratedAuditIds = new List<string> { "preview-001", "preview-002" }
        };
    }

    private List<RecurringAuditSettingSummaryDto> CreateMockRecurringAuditSettings()
    {
        return new List<RecurringAuditSettingSummaryDto>
        {
            new()
            {
                Id = "setting-001",
                SettingName = "Weekly Safety Inspection",
                AuditTemplateName = "Safety Checklist v2.1",
                IsEnabled = true,
                AssignmentType = AssignmentType.Individual,
                AssignToUserName = "John Smith",
                FactoryName = "Main Production Facility",
                AreaName = "Production Floor",
                NextGenerationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(2)),
                LastGeneratedAt = DateTime.UtcNow.AddDays(-7),
                RecurrenceDescription = "Every week on Monday",
                IsReadyToGenerate = false,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-1)
            },
            new()
            {
                Id = "setting-002",
                SettingName = "Monthly Quality Review",
                AuditTemplateName = "Quality Control Template",
                IsEnabled = true,
                AssignmentType = AssignmentType.GroupAny,
                AssignToUserGroupName = "Quality Inspectors",
                FactoryName = "Main Production Facility",
                AreaName = "Quality Control",
                NextGenerationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)),
                LastGeneratedAt = DateTime.UtcNow.AddDays(-30),
                RecurrenceDescription = "Every month on day 1",
                IsReadyToGenerate = true,
                CreatedAt = DateTime.UtcNow.AddDays(-60),
                UpdatedAt = DateTime.UtcNow.AddDays(-5)
            },
            new()
            {
                Id = "setting-003",
                SettingName = "Daily Equipment Check",
                AuditTemplateName = "Equipment Maintenance",
                IsEnabled = false,
                AssignmentType = AssignmentType.GroupAllScheduled,
                AssignToUserGroupName = "Maintenance Crew",
                FactoryName = "Secondary Facility",
                AreaName = "Equipment Bay",
                NextGenerationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
                LastGeneratedAt = DateTime.UtcNow.AddDays(-3),
                RecurrenceDescription = "Every day",
                IsReadyToGenerate = false,
                CreatedAt = DateTime.UtcNow.AddDays(-15),
                UpdatedAt = DateTime.UtcNow.AddDays(-2)
            }
        };
    }
}
