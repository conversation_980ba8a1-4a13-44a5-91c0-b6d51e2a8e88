using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Common.Exceptions;
using HWSAuditPlatform.Application.Common.Interfaces;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Findings.Commands.UpdateFindingStatus;

/// <summary>
/// Handler for updating finding status
/// </summary>
public class UpdateFindingStatusCommandHandler : BaseCommandHandler<UpdateFindingStatusCommand, bool>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public UpdateFindingStatusCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<bool> Handle(UpdateFindingStatusCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Get the finding
        var finding = await _context.Findings
            .FirstOrDefaultAsync(f => f.Id == request.FindingId, cancellationToken);

        if (finding == null)
        {
            throw new NotFoundException("Finding", request.FindingId);
        }

        // Validate status transition
        if (!IsValidStatusTransition(finding.Status, request.Status))
        {
            throw new ValidationException("Status", $"Invalid status transition from {finding.Status} to {request.Status}");
        }

        // Update the status
        finding.Status = request.Status;

        // TODO: Add audit logging for status changes
        // This could create an audit log entry with the status change and notes

        await _context.SaveChangesAsync(cancellationToken);

        return true;
    }

    private static bool IsValidStatusTransition(FindingStatus currentStatus, FindingStatus newStatus)
    {
        // Define valid status transitions
        return currentStatus switch
        {
            FindingStatus.Open => newStatus is FindingStatus.UnderInvestigation or FindingStatus.PendingCorrectiveAction or FindingStatus.Rejected,
            FindingStatus.UnderInvestigation => newStatus is FindingStatus.Open or FindingStatus.PendingCorrectiveAction or FindingStatus.Rejected,
            FindingStatus.PendingCorrectiveAction => newStatus is FindingStatus.PendingVerification or FindingStatus.UnderInvestigation,
            FindingStatus.PendingVerification => newStatus is FindingStatus.Closed or FindingStatus.PendingCorrectiveAction,
            FindingStatus.Closed => newStatus is FindingStatus.Open, // Allow reopening if needed
            FindingStatus.Rejected => newStatus is FindingStatus.Open, // Allow reopening rejected findings
            _ => false
        };
    }
}
