using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Common.Exceptions;
using HWSAuditPlatform.Application.Common.Interfaces;
using HWSAuditPlatform.Application.CorrectiveActions.DTOs;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Infrastructure.Services;

namespace HWSAuditPlatform.Application.CorrectiveActions.Commands.CreateCorrectiveAction;

/// <summary>
/// Handler for creating a new corrective action
/// </summary>
public class CreateCorrectiveActionCommandHandler : BaseCommandHandler<CreateCorrectiveActionCommand, CorrectiveActionDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly ICuidService _cuidService;

    public CreateCorrectiveActionCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ICuidService cuidService)
    {
        _context = context;
        _currentUserService = currentUserService;
        _cuidService = cuidService;
    }

    public override async Task<CorrectiveActionDto> Handle(CreateCorrectiveActionCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Validate that the finding exists
        var finding = await _context.Findings
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
            .FirstOrDefaultAsync(f => f.Id == request.FindingId, cancellationToken);

        if (finding == null)
        {
            throw new NotFoundException("Finding", request.FindingId);
        }

        // Validate that the assigned user exists
        var assignedUser = await _context.Users
            .FirstOrDefaultAsync(u => u.AdObjectGuid == request.AssignedToUserId && u.IsActive, cancellationToken);

        if (assignedUser == null)
        {
            throw new NotFoundException("User", request.AssignedToUserId);
        }

        // Create the corrective action
        var correctiveAction = new CorrectiveAction
        {
            Id = _cuidService.GenerateCuid(),
            FindingId = request.FindingId,
            ActionDescription = request.ActionDescription,
            AssignedToUserId = request.AssignedToUserId,
            AssignedByUserId = currentUserId,
            DueDate = request.DueDate,
            AssignmentNotes = request.AssignmentNotes,
            Status = CorrectiveActionStatus.Assigned
        };

        _context.CorrectiveActions.Add(correctiveAction);
        await _context.SaveChangesAsync(cancellationToken);

        // Update finding status if it's still open
        if (finding.Status == FindingStatus.Open)
        {
            finding.Status = FindingStatus.PendingCorrectiveAction;
            await _context.SaveChangesAsync(cancellationToken);
        }

        // Return the created corrective action as DTO
        return await MapToDto(correctiveAction, assignedUser, cancellationToken);
    }

    private async Task<CorrectiveActionDto> MapToDto(CorrectiveAction correctiveAction, Domain.Entities.Users.User assignedUser, CancellationToken cancellationToken)
    {
        var assignedByUser = await _context.Users
            .FirstOrDefaultAsync(u => u.AdObjectGuid == correctiveAction.AssignedByUserId, cancellationToken);

        var verifiedByUser = !string.IsNullOrEmpty(correctiveAction.VerifiedByUserId)
            ? await _context.Users.FirstOrDefaultAsync(u => u.AdObjectGuid == correctiveAction.VerifiedByUserId, cancellationToken)
            : null;

        return new CorrectiveActionDto
        {
            Id = correctiveAction.Id,
            FindingId = correctiveAction.FindingId,
            ActionDescription = correctiveAction.ActionDescription,
            AssignedToUserId = correctiveAction.AssignedToUserId,
            AssignedToUserName = assignedUser.Username,
            AssignedToUserFullName = assignedUser.FullName,
            AssignedByUserId = correctiveAction.AssignedByUserId,
            AssignedByUserName = assignedByUser?.Username,
            AssignedByUserFullName = assignedByUser?.FullName,
            DueDate = correctiveAction.DueDate,
            CompletionDate = correctiveAction.CompletionDate,
            VerificationDate = correctiveAction.VerificationDate,
            VerifiedByUserId = correctiveAction.VerifiedByUserId,
            VerifiedByUserName = verifiedByUser?.Username,
            VerifiedByUserFullName = verifiedByUser?.FullName,
            Status = correctiveAction.Status,
            AssignmentNotes = correctiveAction.AssignmentNotes,
            CompletionNotes = correctiveAction.CompletionNotes,
            VerificationNotes = correctiveAction.VerificationNotes,
            IsOverdue = correctiveAction.IsOverdue,
            IsCompleted = correctiveAction.IsCompleted,
            IsInProgress = correctiveAction.IsInProgress,
            CreatedAt = correctiveAction.CreatedAt,
            UpdatedAt = correctiveAction.UpdatedAt,
            CreatedBy = correctiveAction.CreatedBy,
            UpdatedBy = correctiveAction.UpdatedBy
        };
    }
}
