using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Common.Interfaces;
using HWSAuditPlatform.Application.CorrectiveActions.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.CorrectiveActions.Queries.GetCorrectiveActions;

/// <summary>
/// Hand<PERSON> for getting corrective actions with filtering and pagination
/// </summary>
public class GetCorrectiveActionsQueryHandler : BaseQueryHandler<GetCorrectiveActionsQuery, PagedResult<CorrectiveActionSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetCorrectiveActionsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PagedResult<CorrectiveActionSummaryDto>> Handle(GetCorrectiveActionsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.CorrectiveActions
            .Include(ca => ca.AssignedToUser)
            .Include(ca => ca.Finding)
                .ThenInclude(f => f.<PERSON>tAnswer)
                    .ThenInclude(aa => aa.Audit)
                        .ThenInclude(a => a.Factory)
            .Include(ca => ca.Finding)
                .ThenInclude(f => f.AuditAnswer)
                    .ThenInclude(aa => aa.Audit)
                        .ThenInclude(a => a.Area)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.FindingId))
        {
            query = query.Where(ca => ca.FindingId == request.FindingId);
        }

        if (!string.IsNullOrEmpty(request.AssignedToUserId))
        {
            query = query.Where(ca => ca.AssignedToUserId == request.AssignedToUserId);
        }

        if (!string.IsNullOrEmpty(request.AssignedByUserId))
        {
            query = query.Where(ca => ca.AssignedByUserId == request.AssignedByUserId);
        }

        if (request.Status.HasValue)
        {
            query = query.Where(ca => ca.Status == request.Status.Value);
        }

        if (request.DueDateFrom.HasValue)
        {
            query = query.Where(ca => ca.DueDate >= request.DueDateFrom.Value);
        }

        if (request.DueDateTo.HasValue)
        {
            query = query.Where(ca => ca.DueDate <= request.DueDateTo.Value);
        }

        if (request.CompletionDateFrom.HasValue)
        {
            query = query.Where(ca => ca.CompletionDate >= request.CompletionDateFrom.Value);
        }

        if (request.CompletionDateTo.HasValue)
        {
            query = query.Where(ca => ca.CompletionDate <= request.CompletionDateTo.Value);
        }

        if (request.IsOverdue.HasValue)
        {
            var today = DateOnly.FromDateTime(DateTime.UtcNow);
            if (request.IsOverdue.Value)
            {
                query = query.Where(ca => ca.DueDate < today && 
                                         ca.Status != CorrectiveActionStatus.VerifiedClosed && 
                                         ca.Status != CorrectiveActionStatus.Cancelled);
            }
            else
            {
                query = query.Where(ca => ca.DueDate >= today || 
                                         ca.Status == CorrectiveActionStatus.VerifiedClosed || 
                                         ca.Status == CorrectiveActionStatus.Cancelled);
            }
        }

        if (request.IsCompleted.HasValue)
        {
            if (request.IsCompleted.Value)
            {
                query = query.Where(ca => ca.Status == CorrectiveActionStatus.VerifiedClosed);
            }
            else
            {
                query = query.Where(ca => ca.Status != CorrectiveActionStatus.VerifiedClosed);
            }
        }

        if (request.IsInProgress.HasValue)
        {
            if (request.IsInProgress.Value)
            {
                query = query.Where(ca => ca.Status == CorrectiveActionStatus.InProgress || 
                                         ca.Status == CorrectiveActionStatus.CompletedPendingVerification);
            }
            else
            {
                query = query.Where(ca => ca.Status != CorrectiveActionStatus.InProgress && 
                                         ca.Status != CorrectiveActionStatus.CompletedPendingVerification);
            }
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(ca => ca.ActionDescription.ToLower().Contains(searchTerm) ||
                                     (ca.AssignmentNotes != null && ca.AssignmentNotes.ToLower().Contains(searchTerm)) ||
                                     (ca.CompletionNotes != null && ca.CompletionNotes.ToLower().Contains(searchTerm)));
        }

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = ApplySorting(query, request.SortBy, request.SortDirection);

        // Apply pagination
        var correctiveActions = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(ca => new CorrectiveActionSummaryDto
            {
                Id = ca.Id,
                FindingId = ca.FindingId,
                FindingCode = ca.Finding.FindingCode,
                ActionDescription = ca.ActionDescription,
                AssignedToUserId = ca.AssignedToUserId,
                AssignedToUserName = ca.AssignedToUser.FullName,
                DueDate = ca.DueDate,
                CompletionDate = ca.CompletionDate,
                Status = ca.Status,
                FactoryName = ca.Finding.AuditAnswer.Audit.Factory.FactoryName,
                AreaName = ca.Finding.AuditAnswer.Audit.Area.AreaName,
                IsOverdue = ca.DueDate < DateOnly.FromDateTime(DateTime.UtcNow) && 
                           ca.Status != CorrectiveActionStatus.VerifiedClosed && 
                           ca.Status != CorrectiveActionStatus.Cancelled,
                IsCompleted = ca.Status == CorrectiveActionStatus.VerifiedClosed,
                IsInProgress = ca.Status == CorrectiveActionStatus.InProgress || 
                              ca.Status == CorrectiveActionStatus.CompletedPendingVerification,
                CreatedAt = ca.CreatedAt
            })
            .ToListAsync(cancellationToken);

        return new PagedResult<CorrectiveActionSummaryDto>
        {
            Items = correctiveActions,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
        };
    }

    private static IQueryable<Domain.Entities.Findings.CorrectiveAction> ApplySorting(
        IQueryable<Domain.Entities.Findings.CorrectiveAction> query, 
        string sortBy, 
        string sortDirection)
    {
        var isDescending = sortDirection.ToLower() == "desc";

        return sortBy.ToLower() switch
        {
            "actiondescription" => isDescending ? query.OrderByDescending(ca => ca.ActionDescription) : query.OrderBy(ca => ca.ActionDescription),
            "assignedto" => isDescending ? query.OrderByDescending(ca => ca.AssignedToUser.FullName) : query.OrderBy(ca => ca.AssignedToUser.FullName),
            "status" => isDescending ? query.OrderByDescending(ca => ca.Status) : query.OrderBy(ca => ca.Status),
            "completiondate" => isDescending ? query.OrderByDescending(ca => ca.CompletionDate) : query.OrderBy(ca => ca.CompletionDate),
            "factory" => isDescending ? query.OrderByDescending(ca => ca.Finding.AuditAnswer.Audit.Factory.FactoryName) : query.OrderBy(ca => ca.Finding.AuditAnswer.Audit.Factory.FactoryName),
            "area" => isDescending ? query.OrderByDescending(ca => ca.Finding.AuditAnswer.Audit.Area.AreaName) : query.OrderBy(ca => ca.Finding.AuditAnswer.Audit.Area.AreaName),
            "createdat" => isDescending ? query.OrderByDescending(ca => ca.CreatedAt) : query.OrderBy(ca => ca.CreatedAt),
            _ => isDescending ? query.OrderByDescending(ca => ca.DueDate) : query.OrderBy(ca => ca.DueDate)
        };
    }
}
