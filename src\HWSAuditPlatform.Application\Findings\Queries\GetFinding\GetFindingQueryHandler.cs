using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Common.Exceptions;
using HWSAuditPlatform.Application.Common.Interfaces;
using HWSAuditPlatform.Application.Findings.DTOs;

namespace HWSAuditPlatform.Application.Findings.Queries.GetFinding;

/// <summary>
/// Handler for getting a single finding by ID
/// </summary>
public class GetFindingQueryHandler : BaseQueryHandler<GetFindingQuery, FindingDto>
{
    private readonly IApplicationDbContext _context;

    public GetFindingQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<FindingDto> Handle(GetFindingQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Findings
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.AuditTemplate)
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.Factory)
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.Area)
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.SubArea)
            .Include(f => f.ReportedByUser)
            .Include(f => f.ResponsibleUser)
            .Include(f => f.RetrospectiveAnalystUser)
            .Include(f => f.FindingCategory);

        if (request.IncludeCorrectiveActions)
        {
            query = query.Include(f => f.CorrectiveActions)
                        .ThenInclude(ca => ca.AssignedToUser);
        }

        var finding = await query
            .FirstOrDefaultAsync(f => f.Id == request.FindingId, cancellationToken);

        if (finding == null)
        {
            throw new NotFoundException("Finding", request.FindingId);
        }

        return new FindingDto
        {
            Id = finding.Id,
            AuditAnswerId = finding.AuditAnswerId,
            AuditId = finding.AuditAnswer.AuditId,
            FindingCode = finding.FindingCode,
            FindingDescription = finding.FindingDescription,
            FindingSeverityLevel = finding.FindingSeverityLevel,
            RootCauseAnalysis = finding.RootCauseAnalysis,
            ImmediateActionTaken = finding.ImmediateActionTaken,
            Status = finding.Status,
            ReportedByUserId = finding.ReportedByUserId,
            ReportedByUserName = finding.ReportedByUser.Username,
            ReportedByUserFullName = finding.ReportedByUser.FullName,
            DueDate = finding.DueDate,
            ResponsibleUserId = finding.ResponsibleUserId,
            ResponsibleUserName = finding.ResponsibleUser?.Username,
            ResponsibleUserFullName = finding.ResponsibleUser?.FullName,
            RetrospectiveAnalystUserId = finding.RetrospectiveAnalystUserId,
            RetrospectiveAnalystUserName = finding.RetrospectiveAnalystUser?.Username,
            RetrospectiveAnalystUserFullName = finding.RetrospectiveAnalystUser?.FullName,
            FindingCategoryId = finding.FindingCategoryId,
            FindingCategoryName = finding.FindingCategory?.CategoryName,
            FindingCategoryColorCode = finding.FindingCategory?.ColorCode,
            FindingCategoryIconName = finding.FindingCategory?.IconName,
            AuditTemplateName = finding.AuditAnswer.Audit.AuditTemplate.TemplateName,
            FactoryName = finding.AuditAnswer.Audit.Factory.FactoryName,
            AreaName = finding.AuditAnswer.Audit.Area.AreaName,
            SubAreaName = finding.AuditAnswer.Audit.SubArea?.SubAreaName,
            CorrectiveActions = request.IncludeCorrectiveActions 
                ? finding.CorrectiveActions.Select(ca => new CorrectiveActionSummaryDto
                {
                    Id = ca.Id,
                    ActionDescription = ca.ActionDescription,
                    AssignedToUserId = ca.AssignedToUserId,
                    AssignedToUserName = ca.AssignedToUser.FullName,
                    DueDate = ca.DueDate,
                    CompletionDate = ca.CompletionDate,
                    Status = ca.Status,
                    IsOverdue = ca.IsOverdue,
                    IsCompleted = ca.IsCompleted,
                    IsInProgress = ca.IsInProgress
                }).ToList()
                : new List<CorrectiveActionSummaryDto>(),
            IsOverdue = finding.IsOverdue,
            IsOpen = finding.IsOpen,
            IsClosed = finding.IsClosed,
            CorrectiveActionCount = finding.CorrectiveActionCount,
            OpenCorrectiveActionCount = finding.OpenCorrectiveActionCount,
            CreatedAt = finding.CreatedAt,
            UpdatedAt = finding.UpdatedAt,
            CreatedBy = finding.CreatedBy,
            UpdatedBy = finding.UpdatedBy
        };
    }
}
