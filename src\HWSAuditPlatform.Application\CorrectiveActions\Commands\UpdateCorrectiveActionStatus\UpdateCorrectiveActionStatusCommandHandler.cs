using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Entities.Findings;

namespace HWSAuditPlatform.Application.CorrectiveActions.Commands.UpdateCorrectiveActionStatus;

/// <summary>
/// Handler for updating corrective action status
/// </summary>
public class UpdateCorrectiveActionStatusCommandHandler : BaseCommandHandler<UpdateCorrectiveActionStatusCommand, bool>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public UpdateCorrectiveActionStatusCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<bool> Handle(UpdateCorrectiveActionStatusCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Get the corrective action with its finding
        var correctiveAction = await _context.CorrectiveActions
            .Include(ca => ca.Finding)
            .FirstOrDefaultAsync(ca => ca.Id == request.CorrectiveActionId, cancellationToken);

        if (correctiveAction == null)
        {
            throw new NotFoundException("CorrectiveAction", request.CorrectiveActionId);
        }

        // Validate status transition
        if (!IsValidStatusTransition(correctiveAction.Status, request.Status))
        {
            throw new ValidationException("Status", $"Invalid status transition from {correctiveAction.Status} to {request.Status}");
        }

        // Validate required fields for specific status changes
        if (request.Status == CorrectiveActionStatus.CompletedPendingVerification && !request.CompletionDate.HasValue)
        {
            throw new ValidationException("CompletionDate", "Completion date is required when marking as completed");
        }

        if (request.Status == CorrectiveActionStatus.VerifiedClosed && !request.VerificationDate.HasValue)
        {
            throw new ValidationException("VerificationDate", "Verification date is required when marking as verified");
        }

        // Update the corrective action
        correctiveAction.Status = request.Status;

        switch (request.Status)
        {
            case CorrectiveActionStatus.InProgress:
                // No additional fields needed
                break;

            case CorrectiveActionStatus.CompletedPendingVerification:
                correctiveAction.CompletionDate = request.CompletionDate;
                correctiveAction.CompletionNotes = request.StatusChangeNotes;
                break;

            case CorrectiveActionStatus.VerifiedClosed:
                correctiveAction.VerificationDate = request.VerificationDate;
                correctiveAction.VerifiedByUserId = currentUserId;
                correctiveAction.VerificationNotes = request.StatusChangeNotes;
                break;

            case CorrectiveActionStatus.Cancelled:
                // Notes stored in assignment notes for cancelled actions
                correctiveAction.AssignmentNotes = request.StatusChangeNotes;
                break;

            case CorrectiveActionStatus.Ineffective:
                correctiveAction.VerificationNotes = request.StatusChangeNotes;
                break;
        }

        await _context.SaveChangesAsync(cancellationToken);

        // Update finding status if all corrective actions are completed
        await UpdateFindingStatusIfNeeded(correctiveAction.Finding, cancellationToken);

        return true;
    }

    private async Task UpdateFindingStatusIfNeeded(Finding finding, CancellationToken cancellationToken)
    {
        // Get all corrective actions for this finding
        var correctiveActions = await _context.CorrectiveActions
            .Where(ca => ca.FindingId == finding.Id)
            .ToListAsync(cancellationToken);

        if (!correctiveActions.Any())
            return;

        // Check if all corrective actions are completed or closed
        var allCompleted = correctiveActions.All(ca => 
            ca.Status == CorrectiveActionStatus.VerifiedClosed || 
            ca.Status == CorrectiveActionStatus.Cancelled);

        var anyPendingVerification = correctiveActions.Any(ca => 
            ca.Status == CorrectiveActionStatus.CompletedPendingVerification);

        if (allCompleted)
        {
            // All corrective actions are done - move finding to pending verification
            finding.Status = FindingStatus.PendingVerification;
        }
        else if (anyPendingVerification && finding.Status == FindingStatus.PendingCorrectiveAction)
        {
            // Some actions completed, move to pending verification
            finding.Status = FindingStatus.PendingVerification;
        }

        await _context.SaveChangesAsync(cancellationToken);
    }

    private static bool IsValidStatusTransition(CorrectiveActionStatus currentStatus, CorrectiveActionStatus newStatus)
    {
        // Define valid status transitions
        return currentStatus switch
        {
            CorrectiveActionStatus.Assigned => newStatus is CorrectiveActionStatus.InProgress or CorrectiveActionStatus.Cancelled,
            CorrectiveActionStatus.InProgress => newStatus is CorrectiveActionStatus.CompletedPendingVerification or CorrectiveActionStatus.Cancelled,
            CorrectiveActionStatus.CompletedPendingVerification => newStatus is CorrectiveActionStatus.VerifiedClosed or CorrectiveActionStatus.Ineffective or CorrectiveActionStatus.InProgress,
            CorrectiveActionStatus.VerifiedClosed => newStatus is CorrectiveActionStatus.Ineffective, // Allow marking as ineffective if needed
            CorrectiveActionStatus.Cancelled => newStatus is CorrectiveActionStatus.Assigned, // Allow reactivation
            CorrectiveActionStatus.Ineffective => newStatus is CorrectiveActionStatus.InProgress, // Allow restart
            _ => false
        };
    }
}
