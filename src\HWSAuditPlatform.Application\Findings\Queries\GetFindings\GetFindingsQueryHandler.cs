using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Common.Interfaces;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Findings.Queries.GetFindings;

/// <summary>
/// Handler for getting findings with filtering and pagination
/// </summary>
public class GetFindingsQueryHandler : BaseQueryHandler<GetFindingsQuery, PagedResult<FindingSummaryDto>>
{
    private readonly IApplicationDbContext _context;

    public GetFindingsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<PagedResult<FindingSummaryDto>> Handle(GetFindingsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Findings
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.Factory)
            .Include(f => f.AuditAnswer)
                .ThenInclude(aa => aa.Audit)
                    .ThenInclude(a => a.Area)
            .Include(f => f.ResponsibleUser)
            .Include(f => f.FindingCategory)
            .Include(f => f.CorrectiveActions)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.AuditId))
        {
            query = query.Where(f => f.AuditAnswer.AuditId == request.AuditId);
        }

        if (request.AuditTemplateId.HasValue)
        {
            query = query.Where(f => f.AuditAnswer.Audit.AuditTemplateId == request.AuditTemplateId.Value);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(f => f.AuditAnswer.Audit.FactoryId == request.FactoryId.Value);
        }

        if (request.AreaId.HasValue)
        {
            query = query.Where(f => f.AuditAnswer.Audit.AreaId == request.AreaId.Value);
        }

        if (request.Status.HasValue)
        {
            query = query.Where(f => f.Status == request.Status.Value);
        }

        if (request.SeverityLevel.HasValue)
        {
            query = query.Where(f => f.FindingSeverityLevel == request.SeverityLevel.Value);
        }

        if (request.FindingCategoryId.HasValue)
        {
            query = query.Where(f => f.FindingCategoryId == request.FindingCategoryId.Value);
        }

        if (!string.IsNullOrEmpty(request.ResponsibleUserId))
        {
            query = query.Where(f => f.ResponsibleUserId == request.ResponsibleUserId);
        }

        if (!string.IsNullOrEmpty(request.ReportedByUserId))
        {
            query = query.Where(f => f.ReportedByUserId == request.ReportedByUserId);
        }

        if (request.DueDateFrom.HasValue)
        {
            query = query.Where(f => f.DueDate >= request.DueDateFrom.Value);
        }

        if (request.DueDateTo.HasValue)
        {
            query = query.Where(f => f.DueDate <= request.DueDateTo.Value);
        }

        if (request.CreatedFrom.HasValue)
        {
            query = query.Where(f => f.CreatedAt >= request.CreatedFrom.Value);
        }

        if (request.CreatedTo.HasValue)
        {
            query = query.Where(f => f.CreatedAt <= request.CreatedTo.Value);
        }

        if (request.IsOverdue.HasValue)
        {
            var today = DateOnly.FromDateTime(DateTime.UtcNow);
            if (request.IsOverdue.Value)
            {
                query = query.Where(f => f.DueDate.HasValue && f.DueDate.Value < today && f.Status != FindingStatus.Closed);
            }
            else
            {
                query = query.Where(f => !f.DueDate.HasValue || f.DueDate.Value >= today || f.Status == FindingStatus.Closed);
            }
        }

        if (request.IsOpen.HasValue)
        {
            if (request.IsOpen.Value)
            {
                query = query.Where(f => f.Status == FindingStatus.Open);
            }
            else
            {
                query = query.Where(f => f.Status != FindingStatus.Open);
            }
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(f => f.FindingDescription.ToLower().Contains(searchTerm) ||
                                    (f.FindingCode != null && f.FindingCode.ToLower().Contains(searchTerm)) ||
                                    (f.RootCauseAnalysis != null && f.RootCauseAnalysis.ToLower().Contains(searchTerm)));
        }

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting
        query = ApplySorting(query, request.SortBy, request.SortDirection);

        // Apply pagination
        var findings = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(f => new FindingSummaryDto
            {
                Id = f.Id,
                FindingCode = f.FindingCode,
                FindingDescription = f.FindingDescription,
                FindingSeverityLevel = f.FindingSeverityLevel,
                Status = f.Status,
                DueDate = f.DueDate,
                ResponsibleUserName = f.ResponsibleUser != null ? f.ResponsibleUser.FullName : null,
                FindingCategoryName = f.FindingCategory != null ? f.FindingCategory.CategoryName : null,
                FindingCategoryColorCode = f.FindingCategory != null ? f.FindingCategory.ColorCode : null,
                AreaName = f.AuditAnswer.Audit.Area.AreaName,
                FactoryName = f.AuditAnswer.Audit.Factory.FactoryName,
                CorrectiveActionCount = f.CorrectiveActions.Count,
                OpenCorrectiveActionCount = f.CorrectiveActions.Count(ca => ca.Status != CorrectiveActionStatus.VerifiedClosed && ca.Status != CorrectiveActionStatus.Cancelled),
                IsOverdue = f.DueDate.HasValue && f.DueDate.Value < DateOnly.FromDateTime(DateTime.UtcNow) && f.Status != FindingStatus.Closed,
                IsOpen = f.Status == FindingStatus.Open,
                CreatedAt = f.CreatedAt
            })
            .ToListAsync(cancellationToken);

        return new PagedResult<FindingSummaryDto>
        {
            Items = findings,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
        };
    }

    private static IQueryable<Domain.Entities.Findings.Finding> ApplySorting(
        IQueryable<Domain.Entities.Findings.Finding> query, 
        string sortBy, 
        string sortDirection)
    {
        var isDescending = sortDirection.ToLower() == "desc";

        return sortBy.ToLower() switch
        {
            "findingcode" => isDescending ? query.OrderByDescending(f => f.FindingCode) : query.OrderBy(f => f.FindingCode),
            "description" => isDescending ? query.OrderByDescending(f => f.FindingDescription) : query.OrderBy(f => f.FindingDescription),
            "severity" => isDescending ? query.OrderByDescending(f => f.FindingSeverityLevel) : query.OrderBy(f => f.FindingSeverityLevel),
            "status" => isDescending ? query.OrderByDescending(f => f.Status) : query.OrderBy(f => f.Status),
            "duedate" => isDescending ? query.OrderByDescending(f => f.DueDate) : query.OrderBy(f => f.DueDate),
            "factory" => isDescending ? query.OrderByDescending(f => f.AuditAnswer.Audit.Factory.FactoryName) : query.OrderBy(f => f.AuditAnswer.Audit.Factory.FactoryName),
            "area" => isDescending ? query.OrderByDescending(f => f.AuditAnswer.Audit.Area.AreaName) : query.OrderBy(f => f.AuditAnswer.Audit.Area.AreaName),
            _ => isDescending ? query.OrderByDescending(f => f.CreatedAt) : query.OrderBy(f => f.CreatedAt)
        };
    }
}
