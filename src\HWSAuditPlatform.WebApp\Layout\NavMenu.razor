﻿@using Microsoft.AspNetCore.Components.Authorization
@inject IConfiguration Configuration

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <i class="bi bi-gear-fill me-2" style="color: var(--industrial-orange);"></i>
            <span style="color: var(--industrial-orange);">HWS</span> AUDIT
        </a>
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="nav flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-speedometer2" aria-hidden="true"></span> Dashboard
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="audits">
                <span class="bi bi-clipboard-check" aria-hidden="true"></span> Audits
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="templates">
                <span class="bi bi-file-earmark-ruled" aria-hidden="true"></span> Templates
            </NavLink>
        </div>

        <AuthorizeView Roles="ProcessOwner,SystemManager,DevAdmin">
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="recurring-audits">
                    <span class="bi bi-arrow-repeat" aria-hidden="true"></span> Recurring Audits
                </NavLink>
            </div>
        </AuthorizeView>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="findings">
                <span class="bi bi-exclamation-triangle" aria-hidden="true"></span> Findings
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="templates/finding-categories">
                <span class="bi bi-tags" aria-hidden="true"></span> Finding Categories
            </NavLink>
        </div>

        <AuthorizeView Roles="SystemManager,DevAdmin">
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="users">
                    <span class="bi bi-people-fill" aria-hidden="true"></span> Users
                </NavLink>
            </div>

            <div class="nav-item px-3">
                <NavLink class="nav-link" href="organization/area-responsibilities">
                    <span class="bi bi-diagram-3" aria-hidden="true"></span> Area Responsibilities
                </NavLink>
            </div>
        </AuthorizeView>

        <hr class="nav-divider" />

        @if (isDevelopment)
        {
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="debug" style="color: var(--industrial-orange);">
                    <span class="bi bi-bug-fill" aria-hidden="true"></span> Debug
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="health" style="color: var(--industrial-orange);">
                    <span class="bi bi-heart-pulse" aria-hidden="true"></span> Health
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="test-error" style="color: var(--industrial-orange);">
                    <span class="bi bi-exclamation-triangle-fill" aria-hidden="true"></span> Test Errors
                </NavLink>
            </div>
        }

        <div class="nav-item px-3">
            <button class="nav-link btn btn-link text-start w-100" @onclick="Logout">
                <span class="bi bi-power" aria-hidden="true"></span> Logout
            </button>
        </div>
    </nav>
</div>

<style>
    .nav-divider {
        border: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin: 10px 15px;
    }

    .nav-link.btn {
        border: none;
        background: none;
        color: inherit;
        padding: 0.5rem 1rem;
        text-decoration: none;
    }

    .nav-link.btn:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
    }
</style>

@code {
    [Inject] private IAuthenticationService AuthService { get; set; } = default!;
    [Inject] private AuthenticationStateProvider AuthStateProvider { get; set; } = default!;
    [Inject] private NavigationManager Navigation { get; set; } = default!;

    private bool isDevelopment = false;

    protected override void OnInitialized()
    {
        // Check if we're in development mode
        isDevelopment = Configuration.GetValue<bool>("Debug:SkipAuthentication");
    }

    private async Task Logout()
    {
        try
        {
            await AuthService.LogoutAsync();

            // Only call MarkUserAsLoggedOut if it's the custom provider
            if (AuthStateProvider is CustomAuthenticationStateProvider customProvider)
            {
                customProvider.MarkUserAsLoggedOut();
            }

            Navigation.NavigateTo("/login", forceLoad: true);
        }
        catch (Exception)
        {
            // Fallback to page reload if logout fails
            Navigation.NavigateTo("/login", forceLoad: true);
        }
    }
}
